const fs = require('fs');
const path = require('path');
const { v2: cloudinary } = require('cloudinary');

// Configure Cloudinary (you'll need to set these environment variables)
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Migration mapping for different image types
const MIGRATION_FOLDERS = {
  trips: 'positive7/trips',
  udbhav: 'positive7/rural-initiatives',
  team: 'positive7/team',
  general: 'positive7/general'
};

// Images to keep local (company assets)
const KEEP_LOCAL = [
  'positive7-logo.png',
  'placeholder.jpg'
];

async function uploadImageToCloudinary(imagePath, folder, publicId) {
  try {
    console.log(`Uploading ${imagePath} to ${folder}/${publicId}...`);
    
    const result = await cloudinary.uploader.upload(imagePath, {
      folder: folder,
      public_id: publicId,
      resource_type: 'auto',
      quality: 'auto',
      format: 'auto',
      overwrite: true
    });

    console.log(`✅ Uploaded: ${result.secure_url}`);
    return {
      success: true,
      url: result.secure_url,
      public_id: result.public_id,
      original_path: imagePath
    };
  } catch (error) {
    console.error(`❌ Failed to upload ${imagePath}:`, error.message);
    return {
      success: false,
      error: error.message,
      original_path: imagePath
    };
  }
}

async function migrateImagesFromDirectory(sourceDir, targetFolder, results) {
  const fullPath = path.join(process.cwd(), 'public', 'images', sourceDir);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`Directory ${sourceDir} does not exist, skipping...`);
    return;
  }

  console.log(`\n📁 Migrating images from ${sourceDir}/`);
  
  const files = fs.readdirSync(fullPath);
  
  for (const file of files) {
    const filePath = path.join(fullPath, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isFile() && /\.(jpg|jpeg|png|webp|gif)$/i.test(file)) {
      // Skip if it's in the keep local list
      if (KEEP_LOCAL.includes(file)) {
        console.log(`⏭️  Skipping ${file} (keeping local)`);
        continue;
      }

      // Generate public_id (remove extension and clean up)
      const publicId = file.replace(/\.[^/.]+$/, '').replace(/[^a-zA-Z0-9-_]/g, '-');
      
      const result = await uploadImageToCloudinary(filePath, targetFolder, publicId);
      results.push(result);
      
      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
}

async function generateMigrationReport(results) {
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  const report = {
    summary: {
      total: results.length,
      successful: successful.length,
      failed: failed.length,
      timestamp: new Date().toISOString()
    },
    successful_uploads: successful.map(r => ({
      original_path: r.original_path,
      cloudinary_url: r.url,
      public_id: r.public_id
    })),
    failed_uploads: failed.map(r => ({
      original_path: r.original_path,
      error: r.error
    }))
  };

  // Save report to file
  const reportPath = path.join(process.cwd(), 'migration-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log('\n📊 Migration Report:');
  console.log(`Total images processed: ${report.summary.total}`);
  console.log(`Successfully uploaded: ${report.summary.successful}`);
  console.log(`Failed uploads: ${report.summary.failed}`);
  console.log(`Report saved to: ${reportPath}`);
  
  if (successful.length > 0) {
    console.log('\n✅ Successfully uploaded images:');
    successful.forEach(r => {
      console.log(`  ${r.original_path} → ${r.url}`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Failed uploads:');
    failed.forEach(r => {
      console.log(`  ${r.original_path}: ${r.error}`);
    });
  }

  return report;
}

async function createUrlMappingFile(results) {
  const successful = results.filter(r => r.success);
  
  // Create a mapping of old local paths to new Cloudinary URLs
  const urlMapping = {};
  
  successful.forEach(result => {
    // Extract relative path from full path
    const relativePath = result.original_path.replace(path.join(process.cwd(), 'public'), '');
    const normalizedPath = relativePath.replace(/\\/g, '/'); // Normalize path separators
    urlMapping[normalizedPath] = result.url;
  });

  // Save URL mapping for easy reference
  const mappingPath = path.join(process.cwd(), 'cloudinary-url-mapping.json');
  fs.writeFileSync(mappingPath, JSON.stringify(urlMapping, null, 2));
  
  console.log(`\n🔗 URL mapping saved to: ${mappingPath}`);
  console.log('Use this file to update image references in your code.');
  
  return urlMapping;
}

async function main() {
  console.log('🚀 Starting image migration to Cloudinary...\n');
  
  // Check if Cloudinary is configured
  if (!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 
      !process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY || 
      !process.env.CLOUDINARY_API_SECRET) {
    console.error('❌ Cloudinary environment variables are not set!');
    console.log('Please set the following environment variables:');
    console.log('- NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME');
    console.log('- NEXT_PUBLIC_CLOUDINARY_API_KEY');
    console.log('- CLOUDINARY_API_SECRET');
    process.exit(1);
  }

  const results = [];

  try {
    // Migrate trip images
    await migrateImagesFromDirectory('trips', MIGRATION_FOLDERS.trips, results);
    
    // Migrate rural initiative images
    await migrateImagesFromDirectory('udbhav', MIGRATION_FOLDERS.udbhav, results);
    
    // Migrate any other images in the root images directory
    const rootImagesPath = path.join(process.cwd(), 'public', 'images');
    if (fs.existsSync(rootImagesPath)) {
      console.log(`\n📁 Migrating root images/`);
      const files = fs.readdirSync(rootImagesPath);
      
      for (const file of files) {
        const filePath = path.join(rootImagesPath, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isFile() && /\.(jpg|jpeg|png|webp|gif)$/i.test(file)) {
          // Skip if it's in the keep local list or if it's a directory
          if (KEEP_LOCAL.includes(file)) {
            console.log(`⏭️  Skipping ${file} (keeping local)`);
            continue;
          }

          const publicId = file.replace(/\.[^/.]+$/, '').replace(/[^a-zA-Z0-9-_]/g, '-');
          const result = await uploadImageToCloudinary(filePath, MIGRATION_FOLDERS.general, publicId);
          results.push(result);
          
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    }

    // Generate reports
    await generateMigrationReport(results);
    await createUrlMappingFile(results);
    
    console.log('\n🎉 Migration completed!');
    console.log('\nNext steps:');
    console.log('1. Review the migration report');
    console.log('2. Update your code to use the new Cloudinary URLs');
    console.log('3. Test your application thoroughly');
    console.log('4. Once confirmed working, you can remove the migrated local images');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  uploadImageToCloudinary,
  migrateImagesFromDirectory,
  generateMigrationReport,
  createUrlMappingFile
};
