import { NextRequest, NextResponse } from 'next/server';
import { generateUploadSignature } from '@/lib/cloudinary';
import { verifyAdminAccess } from '@/lib/auth-server';

// POST /api/cloudinary/sign - Generate signature for Cloudinary uploads
export async function POST(request: NextRequest) {
  try {
    // Verify admin access for uploads
    const { user, hasAccess } = await verifyAdminAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { folder, public_id, transformation, resource_type } = body;

    // Generate upload parameters
    const uploadParams: Record<string, any> = {};
    
    if (folder) uploadParams.folder = folder;
    if (public_id) uploadParams.public_id = public_id;
    if (transformation) uploadParams.transformation = transformation;
    if (resource_type) uploadParams.resource_type = resource_type;

    // Add default parameters
    uploadParams.quality = 'auto';
    uploadParams.format = 'auto';

    // Generate signature
    const signatureData = generateUploadSignature(uploadParams);

    return NextResponse.json({
      ...signatureData,
      ...uploadParams,
    });

  } catch (error: any) {
    console.error('Cloudinary signature error:', error);
    return NextResponse.json(
      { error: 'Failed to generate upload signature', details: error.message },
      { status: 500 }
    );
  }
}
