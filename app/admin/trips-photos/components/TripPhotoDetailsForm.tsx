'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { TripPhotoDetailsFormData } from '@/types/trip-photos';
import { extractFolderIdFromUrl } from '@/lib/google-drive';
import CloudinaryUpload from '@/components/ui/CloudinaryUpload';

interface TripPhotoDetailsFormProps {
  initialData?: Partial<TripPhotoDetailsFormData>;
  onSubmit: (data: TripPhotoDetailsFormData) => Promise<void>;
  onCancel: () => void;
}

export default function TripPhotoDetailsForm({
  initialData,
  onSubmit,
  onCancel,
}: TripPhotoDetailsFormProps) {
  const [formData, setFormData] = useState<TripPhotoDetailsFormData>({
    trip_name: '',
    trip_description: '',
    featured_image_url: '',
    access_password: '',
    google_drive_link: '',
    ...initialData,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [storageUrlError, setStorageUrlError] = useState<string | null>(null);

  const isGooglePhotosLink = (url: string): boolean => {
    return url.includes('photos.google.com') || url.includes('photos.app.goo.gl');
  };

  const isGoogleDriveLink = (url: string): boolean => {
    return url.includes('drive.google.com');
  };

  const validateStorageUrl = (url: string): string | null => {
    if (!url.trim()) return null; // Empty URL is valid

    if (isGoogleDriveLink(url)) {
      if (!url.includes('drive.google.com/drive/folders/')) {
        return 'Please enter a valid Google Drive folder URL (https://drive.google.com/drive/folders/...)';
      }
      
      if (!extractFolderIdFromUrl(url)) {
        return 'Could not extract folder ID from the URL. Please use the format: https://drive.google.com/drive/folders/FOLDER_ID';
      }
    } else if (isGooglePhotosLink(url)) {
      if (!url.includes('/albums/') && !url.includes('/share/')) {
        return 'Please enter a valid Google Photos album URL (https://photos.google.com/albums/... or https://photos.app.goo.gl/...)';
      }
    } else {
      return 'Please enter either a Google Drive folder URL or Google Photos album URL';
    }

    return null;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name === 'google_drive_link') {
      setStorageUrlError(validateStorageUrl(value));
    }
    
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.trip_name) {
      setError('Trip name is required');
      return;
    }
    
    setError(null);
    setIsSubmitting(true);
    
    try {
      await onSubmit(formData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}
      
      <div>
        <label htmlFor="trip_name" className="block text-sm font-medium text-gray-700">
          Trip Name <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          id="trip_name"
          name="trip_name"
          value={formData.trip_name || ''}
          onChange={handleInputChange}
          required
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
        />
      </div>
      
      <div>
        <label htmlFor="trip_description" className="block text-sm font-medium text-gray-700">
          Trip Description
        </label>
        <textarea
          id="trip_description"
          name="trip_description"
          value={formData.trip_description || ''}
          onChange={handleInputChange}
          rows={3}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Featured Image
        </label>
        <CloudinaryUpload
          onUpload={(url) => setFormData({ ...formData, featured_image_url: url })}
          currentImage={formData.featured_image_url}
          uploadType="trip"
          placeholder="Upload trip featured image"
        />
      </div>
      
      <div>
        <label htmlFor="access_password" className="block text-sm font-medium text-gray-700">
          Access Password (Optional)
        </label>
        <input
          type="text"
          id="access_password"
          name="access_password"
          value={formData.access_password || ''}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
          placeholder="Leave blank for no password protection"
        />
        <p className="mt-1 text-xs text-gray-500">
          If provided, this password will be required to access the photos
        </p>
      </div>
      
      <div>
        <label htmlFor="google_drive_link" className="block text-sm font-medium text-gray-700">
          Storage Link (Google Drive/Photos)
        </label>
        <input
          type="url"
          id="google_drive_link"
          name="google_drive_link"
          value={formData.google_drive_link || ''}
          onChange={handleInputChange}
          className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 ${storageUrlError ? 'border-red-300' : ''}`}
          placeholder="https://drive.google.com/drive/folders/... or https://photos.google.com/albums/..."
        />
        <p className="mt-1 text-xs text-gray-500">
          Link to Google Drive folder or Google Photos album
        </p>
        
        {storageUrlError && (
          <p className="mt-1 text-sm text-red-600">
            {storageUrlError}
          </p>
        )}
        
        {formData.google_drive_link && !storageUrlError && (
          <p className="mt-1 text-sm text-green-600">
            {isGoogleDriveLink(formData.google_drive_link) 
              ? `Google Drive Folder ID: ${extractFolderIdFromUrl(formData.google_drive_link)}`
              : isGooglePhotosLink(formData.google_drive_link)
                ? `Google Photos Album Link Valid`
                : null
            }
          </p>
        )}
      </div>
      
      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          disabled={isSubmitting || !!storageUrlError}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          disabled={isSubmitting || !!storageUrlError}
        >
          {isSubmitting ? 'Saving...' : initialData?.trip_name ? 'Update' : 'Create'}
        </button>
      </div>
    </form>
  );
} 