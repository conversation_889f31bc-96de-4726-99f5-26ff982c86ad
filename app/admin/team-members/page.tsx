'use client';

import { useAuth } from '@/lib/hooks/useAuth';
import TeamMemberManagement from '@/components/admin/TeamMemberManagement';
import { AlertTriangle } from 'lucide-react';

export default function TeamMembersPage() {
  const { loading, adminUser } = useAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!adminUser) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
          <p className="text-gray-600">Please log in to access the admin panel.</p>
        </div>
      </div>
    );
  }

  return <TeamMemberManagement />;
}
